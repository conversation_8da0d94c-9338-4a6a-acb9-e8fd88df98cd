"use client";

import React, { useState } from "react";
import { Alert<PERSON>riangle, Eye, Download, Filter } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { PageHeaderWithRefresh } from "@/components/ui/page-header";
import { StatisticsCardsGrid } from "@/components/ui/statistics-card";
import { DataTable, TableColumn } from "@/components/ui/data-table";
import {
  AlertBadge,
  AlertStatusBadge,
  AlertPriorityBadge,
} from "@/components/ui/alert-badge";
import { QuickAlertActions } from "@/components/ui/alert-actions";
import { Button } from "@/components/ui/button";

// Sample alert data interface
interface AlertData {
  id: string;
  type: "critical" | "warning" | "info";
  priority: "high" | "medium" | "low";
  status: "active" | "resolved" | "pending" | "acknowledged";
  title: string;
  titleEn: string;
  description: string;
  descriptionEn: string;
  location: string;
  locationEn: string;
  timestamp: string;
  vehicle: string;
  vehicleEn: string;
  officer: string;
  officerEn: string;
  responseTime: number; // in minutes
}

export default function AllAlertsPage() {
  const { t, language } = useLanguage();
  const [refreshing, setRefreshing] = useState(false);

  // Sample alerts data
  const sampleAlerts: AlertData[] = [
    {
      id: "ALT001",
      type: "critical",
      priority: "high",
      status: "active",
      title: "تجاوز السرعة المحددة",
      titleEn: "Speed Limit Exceeded",
      description: "تجاوز المركبة السرعة المحددة بـ 25 كم/ساعة",
      descriptionEn: "Vehicle exceeded speed limit by 25 km/h",
      location: "الطريق الدائري الشرقي",
      locationEn: "Eastern Ring Road",
      timestamp: "2024-01-15T14:30:00Z",
      vehicle: "شاحنة نقل البضائع - أ ب ج 1234",
      vehicleEn: "Cargo Truck - ABC 1234",
      officer: "أحمد محمد",
      officerEn: "Ahmed Mohammed",
      responseTime: 5,
    },
    {
      id: "ALT002",
      type: "warning",
      priority: "medium",
      status: "pending",
      title: "انحراف عن المسار المحدد",
      titleEn: "Route Deviation",
      description: "انحراف المركبة عن المسار المحدد لأكثر من 2 كم",
      descriptionEn: "Vehicle deviated from designated route by more than 2 km",
      location: "طريق الملك فهد",
      locationEn: "King Fahd Road",
      timestamp: "2024-01-15T13:45:00Z",
      vehicle: "سيارة نقل - د هـ و 5678",
      vehicleEn: "Transport Vehicle - DEF 5678",
      officer: "فاطمة أحمد",
      officerEn: "Fatima Ahmed",
      responseTime: 12,
    },
    {
      id: "ALT003",
      type: "info",
      priority: "low",
      status: "acknowledged",
      title: "توقف غير مجدول",
      titleEn: "Unscheduled Stop",
      description: "توقف المركبة لأكثر من 15 دقيقة في موقع غير مجدول",
      descriptionEn:
        "Vehicle stopped for more than 15 minutes at unscheduled location",
      location: "محطة وقود الخليج",
      locationEn: "Gulf Gas Station",
      timestamp: "2024-01-15T12:20:00Z",
      vehicle: "حافلة نقل - ز ح ط 9012",
      vehicleEn: "Transport Bus - GHI 9012",
      officer: "محمد علي",
      officerEn: "Mohammed Ali",
      responseTime: 8,
    },
  ];

  // Statistics data
  const statisticsData = [
    {
      value: sampleAlerts.filter((alert) => alert.status === "active").length,
      label: t("reportsPages.allAlerts.criticalAlerts"),
      icon: AlertTriangle,
      variant: "danger" as const,
    },
    {
      value: sampleAlerts.filter((alert) => alert.type === "warning").length,
      label: t("reportsPages.allAlerts.warningAlerts"),
      icon: AlertTriangle,
      variant: "warning" as const,
    },
    {
      value: sampleAlerts.filter((alert) => alert.type === "info").length,
      label: t("reportsPages.allAlerts.infoAlerts"),
      icon: AlertTriangle,
      variant: "info" as const,
    },
    {
      value: sampleAlerts.filter((alert) => alert.status === "resolved").length,
      label: t("reportsPages.allAlerts.resolvedAlerts"),
      icon: AlertTriangle,
      variant: "success" as const,
    },
  ];

  // Table columns definition
  const columns: TableColumn<AlertData>[] = [
    {
      key: "type",
      label: t("reportsPages.allAlerts.alertType"),
      sortable: true,
      render: (value, row) => (
        <AlertBadge
          type={value}
          text={
            language === "ar"
              ? value === "critical"
                ? "حرج"
                : value === "warning"
                ? "تحذير"
                : "معلومات"
              : value === "critical"
              ? "Critical"
              : value === "warning"
              ? "Warning"
              : "Info"
          }
          size="sm"
        />
      ),
    },
    {
      key: "priority",
      label: t("common.priority.high"),
      sortable: true,
      render: (value) => <AlertPriorityBadge priority={value} size="sm" />,
    },
    {
      key: "status",
      label: t("common.status.active"),
      sortable: true,
      render: (value) => <AlertStatusBadge status={value} size="sm" />,
    },
    {
      key: "title",
      label: t("reportsPages.allAlerts.alertType"),
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium text-gray-900">
            {language === "ar" ? row.title : row.titleEn}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            {language === "ar" ? row.description : row.descriptionEn}
          </div>
        </div>
      ),
    },
    {
      key: "location",
      label: t("common.location"),
      sortable: true,
      render: (value, row) =>
        language === "ar" ? row.location : row.locationEn,
      hideOnMobile: true,
    },
    {
      key: "vehicle",
      label: t("common.vehicle"),
      sortable: true,
      render: (value, row) => (language === "ar" ? row.vehicle : row.vehicleEn),
      hideOnMobile: true,
    },
    {
      key: "timestamp",
      label: t("reportsPages.allAlerts.alertTime"),
      sortable: true,
      render: (value) =>
        new Date(value).toLocaleString(language === "ar" ? "ar-SA" : "en-US"),
    },
    {
      key: "actions",
      label: t("common.actions"),
      render: (value, row) => (
        <QuickAlertActions
          alertData={row}
          onView={() => handleViewAlert(row)}
          onResolve={
            row.status === "active" ? () => handleResolveAlert(row) : undefined
          }
          onAcknowledge={
            row.status === "pending"
              ? () => handleAcknowledgeAlert(row)
              : undefined
          }
          layout="horizontal"
        />
      ),
    },
  ];

  // Event handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleViewAlert = (alert: AlertData) => {
    console.log("View alert:", alert);
    // Implement view alert logic
  };

  const handleResolveAlert = (alert: AlertData) => {
    console.log("Resolve alert:", alert);
    // Implement resolve alert logic
  };

  const handleAcknowledgeAlert = (alert: AlertData) => {
    console.log("Acknowledge alert:", alert);
    // Implement acknowledge alert logic
  };

  const handleExportData = () => {
    console.log("Export data");
    // Implement export logic
  };

  // Header actions
  const headerActions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={handleExportData}>
        <Download className="h-4 w-4 mr-2" />
        {t("common.actions.export")}
      </Button>
      <Button variant="outline" size="sm">
        <Filter className="h-4 w-4 mr-2" />
        {t("common.filter")}
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeaderWithRefresh
        title={t("reportsPages.allAlerts.title")}
        subtitle={t("reportsPages.allAlerts.description")}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        additionalActions={headerActions}
      />

      {/* Statistics Cards */}
      <StatisticsCardsGrid cards={statisticsData} />

      {/* Alerts Table */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          data={sampleAlerts}
          columns={columns}
          searchable={true}
          searchPlaceholder={t("common.search") + "..."}
          paginated={true}
          defaultPageSize={10}
          emptyMessage={t("common.table.noDataFound")}
        />
      </div>
    </div>
  );
}
