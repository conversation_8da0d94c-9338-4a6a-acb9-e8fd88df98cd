"use client";

import React, { useState } from "react";
import { AlertTriangle, Download, Filter } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { PageHeaderWithRefresh } from "@/components/ui/page-header";
import { StatisticsCardsGrid } from "@/components/ui/statistics-card";
import { DataTable, TableColumn } from "@/components/ui/data-table";
import {
  AlertBadge,
  AlertStatusBadge,
  AlertPriorityBadge,
} from "@/components/ui/alert-badge";
import { QuickAlertActions } from "@/components/ui/alert-actions";
import { Button } from "@/components/ui/button";
import alertsData from "@/data/alerts.json";

// Alert data interface based on JSON structure
interface AlertData {
  id: number;
  type: string;
  severity: "high" | "medium" | "low";
  title: string;
  titleEn: string;
  message: string;
  messageEn: string;
  timestamp: string;
  location: string;
  locationEn: string;
  coordinates: [number, number];
  status: "active" | "resolved" | "pending" | "scheduled";
  assignedTo: string;
  assignedToEn: string;
  relatedVessels: number[];
  actions: Array<{
    action: string;
    actionEn: string;
    timestamp: string;
  }>;
}

export default function AllAlertsPage() {
  const { t, language } = useLanguage();
  const [refreshing, setRefreshing] = useState(false);

  // Load alerts data from JSON
  const sampleAlerts: AlertData[] = alertsData as AlertData[];

  // Statistics data
  const statisticsData = [
    {
      value: sampleAlerts.filter((alert) => alert.status === "active").length,
      label: t("reportsPages.allAlerts.criticalAlerts"),
      icon: AlertTriangle,
      variant: "danger" as const,
    },
    {
      value: sampleAlerts.filter((alert) => alert.severity === "high").length,
      label: t("reportsPages.allAlerts.warningAlerts"),
      icon: AlertTriangle,
      variant: "warning" as const,
    },
    {
      value: sampleAlerts.filter((alert) => alert.severity === "medium").length,
      label: t("reportsPages.allAlerts.infoAlerts"),
      icon: AlertTriangle,
      variant: "info" as const,
    },
    {
      value: sampleAlerts.filter((alert) => alert.status === "resolved").length,
      label: t("reportsPages.allAlerts.resolvedAlerts"),
      icon: AlertTriangle,
      variant: "success" as const,
    },
  ];

  // Table columns definition
  const columns: TableColumn<AlertData>[] = [
    {
      key: "severity",
      label: t("reportsPages.allAlerts.alertType"),
      sortable: true,
      render: (value) => (
        <AlertBadge
          type={
            value === "high"
              ? "critical"
              : value === "medium"
              ? "warning"
              : "info"
          }
          text={
            language === "ar"
              ? value === "high"
                ? "عالية"
                : value === "medium"
                ? "متوسطة"
                : "منخفضة"
              : value === "high"
              ? "High"
              : value === "medium"
              ? "Medium"
              : "Low"
          }
          size="sm"
        />
      ),
    },

    {
      key: "status",
      label: t("common.status.active"),
      sortable: true,
      render: (value) => <AlertStatusBadge status={value} size="sm" />,
    },
    {
      key: "title",
      label: t("reportsPages.allAlerts.alertType"),
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium text-gray-900">
            {language === "ar" ? row.title : row.titleEn}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            {language === "ar" ? row.message : row.messageEn}
          </div>
        </div>
      ),
    },
    {
      key: "location",
      label: t("common.location"),
      sortable: true,
      render: (value, row) =>
        language === "ar" ? row.location : row.locationEn,
      hideOnMobile: true,
    },
    {
      key: "assignedTo",
      label: t("common.assignedTo"),
      sortable: true,
      render: (value, row) =>
        language === "ar" ? row.assignedTo : row.assignedToEn,
      hideOnMobile: true,
    },
    {
      key: "timestamp",
      label: t("reportsPages.allAlerts.alertTime"),
      sortable: true,
      render: (value) =>
        new Date(value).toLocaleString(language === "ar" ? "ar-SA" : "en-US"),
    },
    {
      key: "actions",
      label: t("common.actionsLabel"),
      render: (value, row) => (
        <QuickAlertActions
          alertData={row}
          onView={() => handleViewAlert(row)}
          onResolve={
            row.status === "active" ? () => handleResolveAlert(row) : undefined
          }
          onAcknowledge={
            row.status === "pending"
              ? () => handleAcknowledgeAlert(row)
              : undefined
          }
          layout="horizontal"
        />
      ),
    },
  ];

  // Event handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleViewAlert = (alert: AlertData) => {
    console.log("View alert:", alert);
    // Implement view alert logic
  };

  const handleResolveAlert = (alert: AlertData) => {
    console.log("Resolve alert:", alert);
    // Implement resolve alert logic
  };

  const handleAcknowledgeAlert = (alert: AlertData) => {
    console.log("Acknowledge alert:", alert);
    // Implement acknowledge alert logic
  };

  const handleExportData = () => {
    console.log("Export data");
    // Implement export logic
  };

  // Header actions
  const headerActions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={handleExportData}>
        <Download className="h-4 w-4 mr-2" />
        {t("common.actions.export")}
      </Button>
      <Button variant="outline" size="sm">
        <Filter className="h-4 w-4 mr-2" />
        {t("common.filter")}
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeaderWithRefresh
        title={t("reportsPages.allAlerts.title")}
        subtitle={t("reportsPages.allAlerts.description")}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        additionalActions={headerActions}
      />

      {/* Statistics Cards */}
      <StatisticsCardsGrid cards={statisticsData} />

      {/* Alerts Table */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          data={sampleAlerts}
          columns={columns}
          searchable={true}
          searchPlaceholder={t("common.search") + "..."}
          paginated={true}
          defaultPageSize={10}
          emptyMessage={t("common.table.noDataFound")}
        />
      </div>
    </div>
  );
}
